import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Pencil, PlusIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import type { z } from "zod";
import { FormItem, type FormItemProps } from "~/components/FormItem";
import { Selector } from "~/components/Selector";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";
import { useStore } from "~/hooks/store";
import { FormItemType } from "~/lib/enums/enums";
import { SwimCourseType, swimmingCourseTypes } from "~/lib/enums/swimming";
import { swimmingSessionFormSchema } from "~/lib/schemas";
import type { GetRacesOutput } from "~/server/api/routers/swimmingPta";
import { api } from "~/trpc/react";
import { Checkbox } from "../ui/check-box";
import { SwimmingSessionSelector } from "./SwimmingSessionSelector";

type FormProps = z.infer<typeof swimmingSessionFormSchema>;

interface RaceFormItemProps extends FormItemProps {
  name: keyof FormProps;
}

export const EditSwimmingSession = ({
  races,
  isOfficialRace,
}: {
  races?: GetRacesOutput;
  isOfficialRace: boolean;
}) => {
  const utils = api.useUtils();
  const videoSummary = useStore((state) => state.videoSummary);
  const selectedSwimmingRace = useStore((state) => state.selectedSwimmingRace);
  const selectedSwimmingSession = useStore(
    (state) => state.selectedSwimmingSession,
  );
  const setSelectedSwimmingSession = useStore(
    (state) => state.setSelectedSwimmingSession,
  );

  const [editingSessionId, setEditingSessionId] = useState<string>("");

  const raceField: RaceFormItemProps[] = [
    {
      name: "race_id",
      title: "Race",
      type: FormItemType.select,
      className: "grid",
      placeholder: "Select Race",
      options: [
        ...(races?.map((race) => ({
          label: `${race.stroke_category} ${race.race_distance}m ${race.gender} ${race.age_category}`,
          value: race.race_id,
        })) ?? []),
      ],
    },
  ];

  const nonRaceField: RaceFormItemProps[] = [
    {
      name: "session_type",
      title: "Session Type",
      type: FormItemType.text,
      className: "grid",
    },
    {
      name: "session_description",
      title: "Session Description",
      type: FormItemType.text,
      className: "grid",
    },
  ];

  const baseFormItems: RaceFormItemProps[] = [
    {
      name: "date",
      title: "Date",
      type: FormItemType.date,
      className: "grid",
      disabled: isOfficialRace,
    },
    {
      name: "course_type",
      title: "Course Type",
      type: FormItemType.select,
      className: "grid",
      options: swimmingCourseTypes.map((courseType) => ({
        label: courseType,
        value: courseType,
      })),
      disabled: isOfficialRace,
    },
    {
      name: "is_relay",
      title: "Is Relay",
      type: FormItemType.checkbox,
      className: "grid",
      disabled: isOfficialRace,
      options: [
        {
          label: "Yes",
          value: true as unknown as string,
        },
      ],
      CustomRender: (field) => (
        <Checkbox
          disabled={isOfficialRace}
          checked={field.value as unknown as boolean}
          onCheckedChange={(checked) => {
            field.onChange(checked as unknown as string);
          }}
        />
      ),
    },
  ];
  const officialRaceFormItems = [...raceField, ...baseFormItems];
  const nonOfficialRaceFormItems = [...nonRaceField, ...baseFormItems];

  const formItems = isOfficialRace
    ? officialRaceFormItems
    : nonOfficialRaceFormItems;

  const initialFormData: FormProps = {
    race_id: selectedSwimmingRace?.race_id ?? "",
    date: selectedSwimmingRace?.date
      ? new Date(selectedSwimmingRace.date)
      : new Date(),
    session_type: isOfficialRace ? "Race" : "",
    session_description: isOfficialRace
      ? `${selectedSwimmingRace?.age_category} ${selectedSwimmingRace?.gender} ${selectedSwimmingRace?.race_distance}m ${selectedSwimmingRace?.stroke_category} ${selectedSwimmingRace?.round}`
      : "",
    course_type:
      videoSummary?.competition?.swimCourseType ?? SwimCourseType.long,
    placing: null,
    start_time: null,
    official_time: null,
    result_code: null,
    athleteId: null,
    relay: [],
    is_relay: false,
  };

  const form = useForm<FormProps>({
    resolver: zodResolver(swimmingSessionFormSchema),
    defaultValues: { ...initialFormData },
  });

  const raceId = form.watch("race_id");
  const isRelay = form.watch("is_relay");

  if (isRelay) {
    formItems.push({
      name: "relay",
      title: "Athlete",
      type: FormItemType.checkbox,
      placeholder: "Select Athlete",
      className: "grid",
      options:
        videoSummary?.athletes?.map((athlete) => ({
          label: athlete.name,
          value: athlete.athleteId,
        })) ?? [],
    });
  } else {
    formItems.push({
      name: "athleteId",
      title: "Athlete",
      type: FormItemType.select,
      placeholder: "Select Athlete",
      className: "grid",
      options: videoSummary?.athletes?.map((athlete) => ({
        label: athlete.name,
        value: athlete.athleteId,
      })),
    });
  }

  useEffect(() => {
    if (raceId) {
      const race = races?.find((race) => race.race_id === raceId);
      form.setValue("date", new Date(race?.date ?? ""));
      form.setValue("course_type", race?.course_type ?? SwimCourseType.long);
      form.setValue("is_relay", race?.is_relay ?? false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [raceId]);

  const { data: sessions } = api.swimmingPta.getSessions.useQuery(
    {
      raceId: selectedSwimmingRace?.race_id ?? undefined,
    },
    {
      enabled: !!selectedSwimmingRace,
    },
  );

  useEffect(() => {
    const session = sessions?.find(
      (session) => session.session_id === selectedSwimmingSession?.session_id,
    );
    const hasSelectedSession = selectedSwimmingSession && session;
    if (hasSelectedSession) return;
    if (sessions && sessions.length > 0 && !selectedSwimmingSession) {
      setSelectedSwimmingSession(sessions[0]);
      return;
    }
    if (session) {
      setSelectedSwimmingSession(session);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessions]);

  const { mutate: upsertSession, isPending } =
    api.swimmingPta.upsertSession.useMutation({
      onSuccess: () => {
        toast.success("Session created successfully");
        if (selectedSwimmingRace) {
          void utils.swimmingPta.getSessions.invalidate();
        }
        setEditingSessionId("");
      },
      onError: () => {
        toast.error("Failed to create session");
      },
    });

  const onAddSession = () => {
    const race = races?.find((race) => race.race_id === raceId);
    form.reset({
      ...initialFormData,
      course_type: race?.course_type ?? SwimCourseType.long,
      is_relay: race?.is_relay,
    });
    setEditingSessionId("new");
  };

  const onEditSession = () => {
    if (!selectedSwimmingSession) return;
    const race = races?.find((race) => race.race_id === raceId);
    const sessionAthleteId = selectedSwimmingSession.athlete_id;
    const sessionRelayTeam = selectedSwimmingSession.relay_team;
    const athleteIds = sessionRelayTeam?.athletes
      .filter((x) => {
        const isInOptions = videoSummary?.athletes?.find(
          (y) => y.athleteId === x.athlete_id,
        );
        return isInOptions;
      })
      .map((x) => x.athlete_id);
    form.reset({
      ...selectedSwimmingSession,
      date: new Date(selectedSwimmingSession.date),
      placing: (selectedSwimmingSession.placing ?? "").toString(),
      official_time: (selectedSwimmingSession.official_time ?? "").toString(),
      athleteId: sessionAthleteId,
      relay: athleteIds ?? [],
      is_relay: race
        ? race.is_relay
        : (selectedSwimmingSession.is_relay ?? false),
    });
    setEditingSessionId(selectedSwimmingSession.session_id);
  };

  const onSubmit = (data: FormProps) => {
    let race_id = null;
    if (isOfficialRace) {
      if (!data.race_id || data.race_id === "none") {
        toast.error("Please select a race");
        return;
      }
      race_id = data.race_id;
    }
    if (data.is_relay) {
      if (!data.relay || data.relay.length !== 4) {
        toast.error("Relay team must have 4 athletes");
        return;
      }
    }
    upsertSession({
      sessionId: editingSessionId === "new" ? undefined : editingSessionId,
      ...data,
      race_id,
    });
  };

  const sessionOptions = sessions?.map((session) => {
    const athlete = session.athlete;
    let athleteName = athlete
      ? athlete?.first_name + " " + athlete?.last_name
      : undefined;
    if (!athleteName && session.relay_team) {
      athleteName = session.relay_team.name;
    }
    return {
      label: `${session.session_description} - ${athleteName}`,
      value: session.session_id ?? "",
    };
  });

  return (
    <div className="grid gap-2.5">
      <div className="flex items-end gap-2">
        {isOfficialRace && (
          <Selector
            containerClassName="grid gap-2.5"
            className="w-96 bg-white"
            label="Session"
            options={sessionOptions}
            value={selectedSwimmingSession?.session_id ?? ""}
            onValueChange={(value) => {
              const session =
                sessions?.find((session) => session.session_id === value) ??
                null;
              setSelectedSwimmingSession(session);
            }}
          />
        )}
        {!isOfficialRace && <SwimmingSessionSelector />}

        <Button
          size="icon"
          variant="search"
          disabled={!selectedSwimmingSession}
          onClick={onEditSession}
        >
          <Pencil className="h-3 w-3 text-seaSalt-k40" />
        </Button>
        <Button size="icon" variant="search" onClick={onAddSession}>
          <PlusIcon className="h-3 w-3 text-seaSalt-k40" />
        </Button>
      </div>

      <Dialog
        open={editingSessionId.length > 0}
        onOpenChange={(open) => {
          if (!open) {
            setEditingSessionId("");
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Session</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form
              className="flex flex-col gap-2"
              onSubmit={form.handleSubmit(onSubmit)}
            >
              {formItems.map((item) => (
                <FormItem key={item.name} control={form.control} {...item} />
              ))}
              <Button type="submit" loading={isPending}>
                Save
              </Button>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};
